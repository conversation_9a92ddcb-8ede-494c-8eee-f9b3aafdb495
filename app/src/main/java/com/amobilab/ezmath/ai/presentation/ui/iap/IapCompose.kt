package com.amobilab.ezmath.ai.presentation.ui.iap

import amobi.module.common.advertisements.reward_ad.AdvertsManagerReward
import amobi.module.common.configs.RconfAssist
import amobi.module.common.utils.MixedUtils
import amobi.module.common.views.CommActivity
import amobi.module.compose.extentions.AppPreview
import amobi.module.compose.extentions.PreviewAssist
import amobi.module.compose.extentions.minSize
import amobi.module.compose.foundation.AppBox
import amobi.module.compose.foundation.AppBoxCentered
import amobi.module.compose.foundation.AppButton
import amobi.module.compose.foundation.AppColumn
import amobi.module.compose.foundation.AppColumnCentered
import amobi.module.compose.foundation.AppDivider
import amobi.module.compose.foundation.AppGlideImage
import amobi.module.compose.foundation.AppIcon
import amobi.module.compose.foundation.AppRow
import amobi.module.compose.foundation.AppRowCentered
import amobi.module.compose.foundation.AppSpacer
import amobi.module.compose.foundation.AppText
import amobi.module.compose.foundation.AppTextAutoSize
import amobi.module.compose.theme.AppColors
import amobi.module.compose.theme.AppFontSize
import amobi.module.compose.theme.AppSize
import amobi.module.compose.theme.AppThemeWrapper
import android.app.Activity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.IntentSenderRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Done
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.data.pref.AdIds
import com.amobilab.ezmath.ai.data.pref.RconfConst
import com.amobilab.ezmath.ai.presentation.common.share_dialog.CommonDialog
import com.amobilab.ezmath.ai.presentation.common.shared_components.AppAppbar
import com.amobilab.ezmath.ai.presentation.common.shared_viewmodels.MainDataViewModel
import com.amobilab.ezmath.ai.presentation.common.shared_viewmodels.NavigatorViewModel
import com.amobilab.ezmath.ai.utils.GoogleAuthUiClient
import com.android.billingclient.api.ProductDetails
import com.bumptech.glide.integration.compose.ExperimentalGlideComposeApi
import com.google.android.gms.auth.api.identity.Identity
import kotlinx.coroutines.launch
import java.util.Locale

@AppPreview
@Composable
fun PreviewIapComposePreview() {
    PreviewAssist.initVariables(LocalContext.current)
    AppThemeWrapper {
        IapCompose()
    }
}

@OptIn(ExperimentalGlideComposeApi::class)
@Composable
fun IapCompose() {
    val mainDataViewModel = hiltViewModel<MainDataViewModel>()
    val viewModel = hiltViewModel<IapViewModel>()
    val navigatorViewModel = NavigatorViewModel.getInstance()

    val context = LocalContext.current

    val buy1500CoinProduct
            by viewModel.buy1500CoinProduct.observeAsState(initial = null)
    val buy5500CoinProduct
            by viewModel.buy5500CoinProduct.observeAsState(initial = null)
    val buy12000CoinProduct
            by viewModel.buy12000CoinProduct.observeAsState(initial = null)
    val buy25000CoinProduct
            by viewModel.buy25000CoinProduct.observeAsState(initial = null)
    val buy53000CoinProduct
            by viewModel.buy53000CoinProduct.observeAsState(initial = null)

    val icon1500CoinProduct = R.drawable.ic_1500_coin_product
    val icon5500CoinProduct = R.drawable.ic_5500_coin_product
    val icon12000CoinProduct = R.drawable.ic_12000_coin_product
    val icon25000CoinProduct = R.drawable.ic_25000_coin_product
    val icon53000CoinProduct = R.drawable.ic_53000_coin_product


    val iapProductFetching
            by viewModel.iapProductFetching.observeAsState(initial = true)

    val coroutineScope = rememberCoroutineScope()
    var isLoading by remember { mutableStateOf(false) }
    var isShowLoginPromptDialog by remember { mutableStateOf(false) }
    val googleAuthUiClient by lazy {
        GoogleAuthUiClient(
            oneTapClient = Identity.getSignInClient(context),
            viewModel = mainDataViewModel
        )
    }
    var signedInUser by remember {
        mutableStateOf(
            if (PreviewAssist.IS_PREVIEW) null
            else googleAuthUiClient.getSignedInUser()
        )
    }
    val launcher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartIntentSenderForResult(),
        onResult = { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                isLoading = true
                coroutineScope.launch {
                    val signInResult = googleAuthUiClient.signInWithIntent(
                        intent = result.data ?: return@launch
                    )
                    signedInUser = googleAuthUiClient.getSignedInUser()
//                    viewModel.onSignInResult(signInResult)

                    if (signedInUser != null) {
                        MixedUtils.showToast(context, R.string.login_successful)
                    }
                    isLoading = false
                }
            }
        }
    )
    val gradientBrush = Brush.linearGradient(
        colors = listOf(Color(0xFF81D8D0), Color(0xFF0ABAB5)), // Gradient màu mong muốn
        start = Offset(0f, 0f),
        end = Offset(0f, 100f)
    )

    var selectedPlan by remember { mutableStateOf<ProductDetails?>(null) }
    LaunchedEffect(buy5500CoinProduct) {
        if (buy5500CoinProduct != null && selectedPlan == null) {
            selectedPlan = buy5500CoinProduct
        }
    }

    LaunchedEffect(Unit) {
        viewModel.fetchProduct()
    }

    @Composable
    fun ButtonRewardAd() {
        val isHighlight = false
        AppBox(
            modifier = Modifier
//                .conditional(isHighlight) {
//                    padding(top = 10.dp)
//                }
        ) {
            AppButton(
                modifier = Modifier
                    .minSize(AppSize.MIN_TOUCH_SIZE)
                    .border(
                        width = 1.dp,
                        color = AppColors.current.borderColorButtonGetCredits,
                        shape = RoundedCornerShape(8.dp)
                    ),
                onClick = {
                    if (!AdvertsManagerReward.checkRewardAdverts(AdvertsManagerReward.rewardAds)) {
                        MixedUtils.showToast(context, R.string.reward_ad_not_ready)
                        AdvertsManagerReward.requestRewardAdverts(AdIds.REWARD_CREDIT)
                        return@AppButton
                    }
                    AdvertsManagerReward.showRewardAd(
                        (context as CommActivity),
                        AdvertsManagerReward.rewardAds,
                        onUserEarnedReward = {
                            viewModel.onRewardAdComplete(context)
                        },
                    )
                },
                colors = ButtonDefaults.buttonColors(
                    containerColor =
                        if (isHighlight) AppColors.current.buttonActiveGetCredits
                        else Color.Transparent,
                    disabledContainerColor = AppColors.current.buttonInactiveGetCredits,
                    contentColor = AppColors.current.buttonText,
                    disabledContentColor = AppColors.current.buttonInactiveText
                ),
                contentPadding = PaddingValues(horizontal = 16.dp)
            ) {
                AppRow(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    AppTextAutoSize(
                        text = stringResource(R.string.txtid_watch_ad) + ": " +
                                stringResource(
                                    R.string.get_number_coins,
                                    String.format(
                                        Locale.getDefault(),
                                        "%,d",
                                        RconfAssist.getInt(RconfConst.CREDIT_WATCH_REWARD_AD)
                                    )
                                ),
                        modifier = Modifier.weight(1f),
                        fontSize = AppFontSize.BODY2,
                        color = AppColors.current.onText,
                        fontWeight = FontWeight.Bold,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis,
                        lineHeight = 20.sp
                    )

                    AppSpacer(16.dp)

                    AppText(
                        text = stringResource(R.string.txtid_free),
                        fontSize = AppFontSize.BODY2,
                        color = AppColors.current.moneyTextColor,
                        fontWeight = FontWeight.Bold,
                        maxLines = 1,
                        lineHeight = 20.sp
                    )
                }
            }
        }
    }

    @Composable
    fun ButtonIAP(productDetails: ProductDetails?, coinNumber: Long, iconRes: Int = icon1500CoinProduct) {
        val isHighlight = productDetails == buy5500CoinProduct || productDetails == buy53000CoinProduct
        val isSelected = productDetails == selectedPlan

        val backgroundColor = when {
            isSelected -> AppColors.current.buttonActiveGetCredits
//            isHighlight -> AppColors.current.buttonActive
            else -> AppColors.current.buttonInactiveGetCredits
        }

        AppBox(
            modifier = Modifier
//                .conditional(isHighlight) {
//                    padding(top = 10.dp)
//                }
        ) {
            AppButton(
                modifier = Modifier
                    .minSize(AppSize.MIN_TOUCH_SIZE)
                    .border(
                        width = 1.dp,
                        color = AppColors.current.borderColorButtonGetCredits,
                        shape = RoundedCornerShape(8.dp)
                    ),
                onClick = {
//                    if (signedInUser == null) {
//                        isShowLoginPromptDialog = true
//                        return@AppButton
//                    }
//                    viewModel.startPurchaseIAP(context, productDetails)

                    selectedPlan = productDetails
                },
                colors = ButtonDefaults.buttonColors(
                    containerColor = backgroundColor,
                    disabledContainerColor = AppColors.current.buttonInactiveGetCredits,
                    contentColor = AppColors.current.buttonText,
                    disabledContentColor = AppColors.current.buttonInactiveText
                ),
                contentPadding = PaddingValues(vertical = 2.dp,horizontal = 8.dp)
            ) {
                AppRow(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    AppGlideImage(
                        resId = iconRes,
                        modifier = Modifier
                            .size(48.dp),
                    )

                    AppSpacer(8.dp)

                    AppTextAutoSize(
                        text = "+${String.format(Locale.getDefault(), "%,d", coinNumber)}",
                        modifier = Modifier.weight(1f),
                        fontSize = AppFontSize.BODY2,
                        color = AppColors.current.onText,
                        fontWeight = FontWeight.Bold,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis,
                        lineHeight = 20.sp
                    )

                    AppSpacer(16.dp)

                    AppBox(
                        modifier = Modifier
                            .background(
                                AppColors.current.backgroundButtonPrice,
                                shape = RoundedCornerShape(8.dp)
                            )
                            .border(
                                width = 1.dp,
                                color = Color(0xFF0AB5B0),
                                shape = RoundedCornerShape(8.dp)
                            )
                            .padding(horizontal = 8.dp, vertical = 8.dp)
                    ){
                        AppText(
                            text = productDetails?.oneTimePurchaseOfferDetails?.formattedPrice ?: "",
                            fontSize = AppFontSize.BODY2,
                            color = AppColors.current.moneyTextColor,
                            fontWeight = FontWeight.Bold,
                            maxLines = 1,
                            lineHeight = 20.sp
                        )
                    }
                }
            }

            if (isHighlight) {
                Box(
                    modifier = Modifier
                        .offset(56.dp, (-6).dp)
                        .wrapContentSize()
                ) {
                    AppGlideImage(
                        resId = R.drawable.bg_most_popular,
                        modifier = Modifier
                            .matchParentSize()
                    )
                    AppText(
                        text = stringResource(R.string.txtid_most_popular),
                        fontSize = AppFontSize.SMALL,
                        fontWeight = FontWeight.Bold,
                        color = AppColors.current.onText,
                        lineHeight = 16.sp,
                        modifier = Modifier
                            .padding(horizontal = 16.dp, vertical = 2.dp)
                            .align(Alignment.Center)
                    )
                }
            }

        }
    }

    var isNavigatingBack by remember { mutableStateOf(false) }

    Scaffold { innerPadding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
        ) {
            AppColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .background(AppColors.current.backgroundGetCredits)
            ) {
//                AppSpacer(AppSize.APPBAR_HEIGHT)
                AppGlideImage(
                    modifier = Modifier
                        .fillMaxSize(),
                    resId = R.drawable.bg_get_credits_new
                )
            }
            AppColumn(
                Modifier
                    .fillMaxSize()
                    .padding(bottom = innerPadding.calculateBottomPadding())
            ) {
                AppAppbar(
                    innerPadding = innerPadding,
                    title = stringResource(R.string.txtid_get_credits),
                    onBack = {
                        if (!isNavigatingBack) {
                            isNavigatingBack = true
                            navigatorViewModel.navigateBack()
                        }
                    },
                    color = AppColors.current.onText
                )
                AppSpacer(12.dp)
                AppColumn(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(horizontal = 16.dp)
                ) {
                    AppColumn(
                        modifier = Modifier
                            .weight(1f)
                            .verticalScroll(rememberScrollState())
                    ) {
                        AppTextAutoSize(
                            text = stringResource(R.string.txtid_ai_chat_plus),
                            style = TextStyle(
                                brush = gradientBrush, // Áp dụng màu gradient
                            ),
                            fontSize = AppFontSize.BIG,
                            fontWeight = FontWeight.Bold,
                            lineHeight = 28.sp,
                            maxLines = 1,
                            color = Color.Unspecified
                        )
                        AppSpacer(4.dp)
                        AppTextAutoSize(
                            text = stringResource(R.string.access_advanced_features),
                            fontSize = AppFontSize.BODY1,
                            fontWeight = FontWeight.Normal,
                            maxLines = 2,
                            color = AppColors.current.onText,
                        )
                        AppSpacer(12.dp)

                        val listBenefits = listOf(
                            Triple(
                                R.string.save_history,
                                R.string.txtid_never_lose_important_chat,
                                R.drawable.ic_save_history_credits
                            ),
                            Triple(
                                R.string.mark_content,
                                R.string.txtid_highlight_info,
                                R.drawable.ic_mark_content_credits
                            ),
                            Triple(
                                R.string.use_anytime,
                                R.string.txtid_24_7_access,
                                R.drawable.ic_use_anytime_credits
                            ),
                        )

                        AppColumn(
                            modifier = Modifier
                                .fillMaxWidth()
                                .background(
                                    AppColors.current.buttonInactiveGetCredits,
                                    shape = RoundedCornerShape(8.dp)
                                )
                                .border(
                                    width = 1.dp,
                                    color = AppColors.current.borderColorButtonGetCredits,
                                    shape = RoundedCornerShape(8.dp)
                                )
                                .padding(12.dp)
                        ) {
                            for (benefit in listBenefits) {
                                AppRow(
                                    modifier = Modifier.fillMaxWidth(),
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    AppSpacer(4.dp)
                                    AppGlideImage(
                                        resId = benefit.third,
                                        modifier = Modifier
                                            .size(32.dp),
                                    )
                                    AppSpacer(8.dp)
                                    AppColumn {
                                        AppText(
                                            text = stringResource(benefit.first),
                                            fontSize = AppFontSize.BODY2,
                                            fontWeight = FontWeight.W700,
                                            color = AppColors.current.onText,
                                            lineHeight = 20.sp
                                        )
                                        AppText(
                                            text = stringResource(benefit.second),
                                            fontSize = AppFontSize.BODY2,
                                            fontWeight = FontWeight.Normal,
                                            color = AppColors.current.textHintColorItemGetCredits,
                                            lineHeight = 20.sp
                                        )
                                    }
                                }

                                if (benefit != listBenefits.last())
                                    AppSpacer(8.dp)
                            }
                        }

                        AppSpacer(modifier = Modifier.weight(2f))

                        AppSpacer(20.dp)

                        AppText(
                            modifier = Modifier.padding(start = 16.dp),
                            text = stringResource(R.string.txtid_choose_a_plan).uppercase(),
                            fontSize = AppFontSize.BODY2,
                            fontWeight = FontWeight.W400,
                            color = AppColors.current.textHintColor
                        )

                        AppColumnCentered(
                            modifier = Modifier
                                .fillMaxWidth(),
                        ) {
                            AppSpacer(4.dp)
                            ButtonRewardAd()
                            AppSpacer(12.dp)
                            ButtonIAP(
                                buy1500CoinProduct,
                                RconfAssist.getLong(RconfConst.NUM_COINS_PURCHASE_1500),
                                icon1500CoinProduct
                            )
                            AppSpacer(12.dp)
                            ButtonIAP(
                                buy5500CoinProduct,
                                RconfAssist.getLong(RconfConst.NUM_COINS_PURCHASE_5500),
                                icon5500CoinProduct
                            )
                            AppSpacer(12.dp)
                            ButtonIAP(
                                buy12000CoinProduct,
                                RconfAssist.getLong(RconfConst.NUM_COINS_PURCHASE_12000),
                                icon12000CoinProduct
                            )
                            AppSpacer(12.dp)
                            ButtonIAP(
                                buy25000CoinProduct,
                                RconfAssist.getLong(RconfConst.NUM_COINS_PURCHASE_25000),
                                icon25000CoinProduct
                            )
                            AppSpacer(12.dp)
                            ButtonIAP(
                                buy53000CoinProduct,
                                RconfAssist.getLong(RconfConst.NUM_COINS_PURCHASE_53000),
                                icon53000CoinProduct
                            )
                        }

                        AppSpacer(12.dp)

                        AppText(
                            modifier = Modifier.padding(start = 16.dp),
                            text = stringResource(R.string.txtid_or).uppercase(),
                            fontSize = AppFontSize.BODY2,
                            fontWeight = FontWeight.W400,
                            color = AppColors.current.textHintColor,
                            lineHeight = 20.sp
                        )

    
                        AppSpacer(16.dp)
                    }

                    AppDivider(thickness = 1.dp, color = Color(0xFF2C3E50))

                    AppSpacer(16.dp)

                    AppButton(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(48.dp),
                        onClick = {
                            if (selectedPlan != null) {
                                if (signedInUser == null) {
                                    isShowLoginPromptDialog = true
                                } else {
                                    viewModel.startPurchaseIAP(context, selectedPlan)
                                }
                            }
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color.Transparent,
                            contentColor = AppColors.current.onText
                        ),
                        shape = RoundedCornerShape(8.dp),
                        enabled = selectedPlan != null,
                        contentPadding = PaddingValues(0.dp)
                    ) {
                        Box(
                            modifier = Modifier
                                .fillMaxSize()
                                .background(
                                    brush = gradientBrush,
                                    shape = RoundedCornerShape(8.dp)
                                )
                                .padding(horizontal = 16.dp, vertical = 12.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            AppText(
                                text = stringResource(R.string.buy_coins),
                                fontSize = AppFontSize.BODY1,
                                fontWeight = FontWeight.W500,
                                color = AppColors.current.onText,
                                lineHeight = 24.sp
                            )
                        }
                    }
                    AppSpacer(16.dp)
                }
            }
        }
        if (isLoading) {
            AppBoxCentered(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.5f))
                    .clickable(enabled = false) {},
            ) {
                CircularProgressIndicator()
            }
        }

        if (isShowLoginPromptDialog) {
            CommonDialog(
                title = stringResource(R.string.dialog_need_account_title),
                message = stringResource(R.string.dialog_need_account_body)
                        + "\n"
                        + stringResource(
                    R.string.txtid_sign_in_reward,
                    String.format(
                        Locale.getDefault(),
                        "%,d",
                        RconfAssist.getInt(RconfConst.CREDIT_SIGN_IN_REWARD)
                    )
                ),
                confirmText = stringResource(R.string.sign_in),
                dismissText = stringResource(R.string.txtid_cancel),
                onConfirm = {
                    isShowLoginPromptDialog = false
                    coroutineScope.launch {
                        val signInIntentSender = googleAuthUiClient.signIn()
                        launcher.launch(
                            IntentSenderRequest
                                .Builder(signInIntentSender ?: return@launch)
                                .build()
                        )
                    }
                },
                onDismiss = { isShowLoginPromptDialog = false },
            )
        }
    }
}